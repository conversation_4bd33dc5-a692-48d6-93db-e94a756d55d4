import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import PasswordSetupModal from '@/components/PasswordSetupModal';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isAnonymous: boolean;
  needsEmailUpgrade: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: Error | null }>;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signInWithGoogle: (redirectUrl?: string) => Promise<{ error: Error | null }>;
  signInAnonymously: () => Promise<{ error: Error | null }>;
  upgradeToEmail: (email: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Helper function to clear Supabase auth tokens from localStorage
const clearAuthTokens = () => {
  try {
    // Clear all Supabase auth-related items from localStorage
    const keysToRemove: string[] = [];
    
    // Iterate through localStorage to find Supabase auth keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('sb-') && key.includes('-auth-token')) {
        keysToRemove.push(key);
      }
    }
    
    // Remove the identified keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log('🧹 Cleared auth tokens from localStorage');
  } catch (error) {
    console.error('Error clearing auth tokens:', error);
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPasswordSetup, setShowPasswordSetup] = useState(false);

  const { toast } = useToast();

  // Helper function to detect if a user is anonymous
  const isUserAnonymous = (user: User | null): boolean => {
    if (!user) return false;

    // Check multiple sources for anonymous user detection
    // 1. Check raw_user_meta_data.is_anonymous (most reliable)
    const isAnonymousFromMetadata = user.user_metadata?.is_anonymous === true;

    // 2. Check if user has no email and is_anonymous flag
    const hasNoEmailButIsAnonymous = !user.email && user.user_metadata?.is_anonymous === true;

    // 3. Fallback: check built-in is_anonymous property if available
    const isAnonymousBuiltIn = (user as User & { is_anonymous?: boolean }).is_anonymous === true;

    return isAnonymousFromMetadata || hasNoEmailButIsAnonymous || isAnonymousBuiltIn;
  };

  // Helper function to determine if anonymous user needs email upgrade
  const doesAnonymousUserNeedEmailUpgrade = (user: User | null): boolean => {
    if (!user) return false;
    // Only require email upgrade if user is anonymous AND has no email AND has hit message limits
    // For now, we'll let the Chat component handle the message limit logic
    // This function should only return true for users who explicitly need email to continue
    // We'll return false here and let the Chat component manage the 3-message limit
    return false;
  };

  const checkPasswordSetupRequired = (user: User | null) => {
    if (user) {
      const hasPassword = user.user_metadata?.hasPassword;
      const isOAuthUser = user.app_metadata?.providers?.includes('google') || 
                         user.app_metadata?.provider === 'google' ||
                         user.identities?.some(identity => identity.provider === 'google');
      
      // Show password setup modal if:
      // 1. User explicitly has hasPassword set to false (from email signup without password completion)
      // 2. AND user is not an OAuth user (Google, etc.)
      // 3. AND user is not anonymous (anonymous users don't need passwords until they upgrade)
      if (hasPassword === false && !isOAuthUser && !isUserAnonymous(user)) {
        setShowPasswordSetup(true);
      } else {
        setShowPasswordSetup(false);
      }
    } else {
      setShowPasswordSetup(false);
    }
  };

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔐 Auth state changed:', event, session?.user?.email);
        console.log('🔐 Current URL at auth change:', window.location.href);
        console.log('🔐 Auth event details:', { 
          event, 
          hasSession: !!session, 
          userId: session?.user?.id,
          isAnonymous: session?.user ? isUserAnonymous(session.user) : false,
          userMetadata: session?.user?.user_metadata
        });
        
        setSession(session);
        setUser(session?.user ?? null);
        
        // Check if password setup is required
        checkPasswordSetupRequired(session?.user ?? null);
        
        // Only set loading to false for certain events to avoid premature state changes
        if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
          console.log('🔐 Setting loading to false for event:', event);
          setLoading(false);
        }
      }
    );

    // Check for existing session but don't auto sign-in anonymously
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      if (session) {
        console.log('🔐 Found existing session:', { 
          userId: session.user.id, 
          isAnonymous: isUserAnonymous(session.user),
          email: session.user.email 
        });
        setSession(session);
        setUser(session?.user ?? null);
        checkPasswordSetupRequired(session?.user ?? null);
      } else {
        console.log('🔐 No existing session found - waiting for user action to sign in');
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePasswordSetupComplete = () => {
    setShowPasswordSetup(false);
    // Refresh user data to get updated metadata
    supabase.auth.getUser().then(({ data: { user } }) => {
      if (user) {
        setUser(user);
        checkPasswordSetupRequired(user);
      }
    });
  };

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      const redirectUrl = `${window.location.origin}${window.location.pathname}`;
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: fullName ? { full_name: fullName } : {}
        }
      });

      if (error) {
        // Clear tokens on auth error
        clearAuthTokens();
        toast({
          title: "Sign up failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Check your email",
        description: "We've sent you a confirmation link.",
      });

      return { error: null };
    } catch (error) {
      console.error('Sign up error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Clear tokens on auth error
        clearAuthTokens();
        toast({
          title: "Sign in failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Welcome back!",
        description: "You've successfully signed in.",
      });

      return { error: null };
    } catch (error) {
      console.error('Sign in error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const signInWithGoogle = async (redirectUrl?: string) => {
    try {
      const finalRedirectUrl = redirectUrl || `${window.location.origin}${window.location.pathname}`;
      
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: finalRedirectUrl
        }
      });

      if (error) {
        // Clear tokens on auth error
        clearAuthTokens();
        toast({
          title: "Google sign in failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error('Google sign in error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const signInAnonymously = async () => {
    try {
      console.log('🔐 Attempting anonymous sign-in...');
      const { data, error } = await supabase.auth.signInAnonymously({
        options: {
          data: {
            is_anonymous: true,
            anonymous_created_at: new Date().toISOString(),
          }
        }
      });

      if (error) {
        // Clear tokens on auth error
        clearAuthTokens();
        console.error('🔐 Anonymous sign-in failed:', error);
        toast({
          title: "Anonymous sign in failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      console.log('🔐 Anonymous sign-in successful:', { 
        userId: data.user?.id,
        isAnonymous: data.user ? isUserAnonymous(data.user) : false 
      });

      toast({
        title: "Welcome!",
        description: "You can now explore the app. You'll be prompted to provide an email after 3 messages.",
      });

      return { error: null };
    } catch (error) {
      console.error('Anonymous sign in error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const upgradeToEmail = async (email: string) => {
    try {
      console.log('🔐 Attempting to upgrade anonymous user to email:', email);
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        const error = new Error('Invalid email format');
        toast({
          title: "Invalid email",
          description: "Please enter a valid email address.",
          variant: "destructive",
        });
        return { error };
      }

      // Check if user is currently anonymous
      if (!user || !isUserAnonymous(user)) {
        const error = new Error('User is not anonymous');
        toast({
          title: "Email upgrade failed",
          description: "Only anonymous users can be upgraded to email accounts.",
          variant: "destructive",
        });
        return { error };
      }

      // Call the backend API to upgrade to email (no verification required)
      const { data, error } = await supabase.functions.invoke('api/auth/upgrade-to-email', {
        body: { email }
      });

      if (error || !data?.success) {
        // Clear tokens on auth error
        clearAuthTokens();
        console.error('🔐 Email upgrade failed:', error);
        
        const errorMessage = error?.message || data?.error || 'Failed to upgrade account';
        
        // Handle specific error cases
        if (errorMessage.includes('email_address_not_authorized')) {
          toast({
            title: "Email upgrade failed",
            description: "This email address is not authorized. Please try a different email.",
            variant: "destructive",
          });
        } else if (errorMessage.includes('User already registered') || errorMessage.includes('email already')) {
          toast({
            title: "Email already in use",
            description: "This email is already associated with another account. Please sign in to that account or use a different email.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Email upgrade failed",
            description: errorMessage,
            variant: "destructive",
          });
        }
        return { error: new Error(errorMessage) };
      }

      console.log('🔐 Email upgrade successful:', { 
        userId: user.id,
        newEmail: email
      });

      // Refresh the auth state to get updated user info
      await supabase.auth.getUser();

      toast({
        title: "Seat reserved successfully!",
        description: "Your email has been linked and you can continue your conversations immediately.",
      });

      return { error: null };
    } catch (error) {
      console.error('Email upgrade error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const signOut = async () => {
    try {
      // Clear tokens before signing out
      clearAuthTokens();
      
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast({
          title: "Sign out failed",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Signed out",
          description: "You've been successfully signed out.",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('Sign out error:', error);
      // Clear tokens even if sign out fails
      clearAuthTokens();
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const redirectUrl = `${window.location.origin}/reset-password`;
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl
      });

      if (error) {
        // Clear tokens on auth error
        clearAuthTokens();
        toast({
          title: "Password reset failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Check your email",
        description: "We've sent you a password reset link.",
      });

      return { error: null };
    } catch (error) {
      console.error('Password reset error:', error);
      // Clear tokens on auth error
      clearAuthTokens();
      return { error };
    }
  };

  const value = {
    user,
    session,
    loading,
    isAnonymous: user ? isUserAnonymous(user) : false,
    needsEmailUpgrade: user ? doesAnonymousUserNeedEmailUpgrade(user) : false,
    signUp,
    signIn,
    signInWithGoogle,
    signInAnonymously,
    upgradeToEmail,
    signOut,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      <PasswordSetupModal
        isOpen={showPasswordSetup}
        onClose={handlePasswordSetupComplete}
        userEmail={user?.email}
        isRequired={true}
      />
    </AuthContext.Provider>
  );
};
