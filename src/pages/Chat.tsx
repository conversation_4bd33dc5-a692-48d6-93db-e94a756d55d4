import React, { useState, useRef, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ArrowUp, User, AlertCircle, Crown, CreditCard } from 'lucide-react';
import { useConversations } from '@/hooks/useConversations';
import { useCharacters, type Character } from '@/hooks/useCharacters';
import { useAuth } from '@/contexts/AuthContext';
import { useUsage } from '@/contexts/UsageContext';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import { Alert, AlertDescription } from '@/components/ui/alert';
import PaygCreditsModal from '@/components/PaygCreditsModal';
import EmailUpgradeModal from '@/components/EmailUpgradeModal';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'character';
  timestamp: Date;
}

interface UsageLimitError {
  error: string;
  message: string;
  limits: {
    messages: number;
  };
  currentUsage: {
    messages: number;
  };
  subscriptionTier: string;
  upgradeRequired: boolean;
}

const Chat = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { characterId } = useParams();
  const { user, loading, isAnonymous, needsEmailUpgrade } = useAuth();
  const { createConversation, saveMessage, loadConversationMessages } = useConversations();
  const { data: characters } = useCharacters();
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [character, setCharacter] = useState<Character | null>(null);
  const [usageLimitError, setUsageLimitError] = useState<UsageLimitError | null>(null);
  const [isPaygModalOpen, setIsPaygModalOpen] = useState(false);
  const [isEmailUpgradeModalOpen, setIsEmailUpgradeModalOpen] = useState(false);
  const [messageCount, setMessageCount] = useState(0);
  const [anonymousMessageCount, setAnonymousMessageCount] = useState(0);

  // Redirect unauthenticated users
  useEffect(() => {
    console.log('Auth check - loading:', loading, 'user:', user ? 'authenticated' : 'not authenticated');
    if (!loading && !user) {
      console.log('User not authenticated, redirecting to auth page');
      navigate('/auth');
    }
  }, [user, loading]);

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize character and conversation
  useEffect(() => {
    const initializeChat = async () => {
      console.log('Initialize chat - characters:', characters?.length, 'loading:', loading, 'user:', user ? 'authenticated' : 'not authenticated', 'characterId:', characterId);
      if (!characters || loading || !user) return; // Wait for characters and auth to load
      
      // Get character from URL or location state
      const charId = characterId || location.state?.character?.id;
      console.log('Character ID from params or state:', charId);
      const foundCharacter = characters.find(c => c.id === charId);
      console.log('Found character:', foundCharacter ? foundCharacter.name : 'NOT FOUND');
      
      if (!foundCharacter) {
        navigate('/');
        return;
      }

      setCharacter(foundCharacter);

      // Check if we're continuing an existing conversation
      const continueConversation = location.state?.continueConversation;
      
      if (continueConversation && continueConversation.id) {
        // Load existing conversation messages
        try {
          const loadedMessages = await loadConversationMessages(continueConversation.id);
          const formattedMessages: Message[] = loadedMessages.map(msg => ({
            id: msg.id,
            content: msg.content,
            sender: msg.sender_type as 'user' | 'character',
            timestamp: new Date(msg.created_at),
          }));
          
          setMessages(formattedMessages);
          setCurrentConversationId(continueConversation.id);
        } catch (error) {
          console.error('Failed to load conversation:', error);
        }
      } else {
        // For new conversations from Index page, show initial message
        // Conversation will be created when user sends first message
        const initialMessage: Message = {
          id: '1',
          content: foundCharacter.initialMessage,
          sender: 'character',
          timestamp: new Date(),
        };
        
        setMessages([initialMessage]);
        // currentConversationId remains null for new conversations
      }
    };

    initializeChat();
  }, [characterId, location.state, characters, loading, user]);

  // Check for anonymous users who have already reached 3+ messages on page load
  useEffect(() => {
    if (!loading && user && isAnonymous && messages.length > 0) {
      const userMessageCount = messages.filter(m => m.sender === 'user').length;
      if (userMessageCount >= 3 && !isEmailUpgradeModalOpen) {
        console.log('📄 Page load: Anonymous user already has 3+ messages, showing email upgrade modal');
        setMessageCount(userMessageCount);
        setAnonymousMessageCount(userMessageCount);
        setIsEmailUpgradeModalOpen(true);
      }
    }
  }, [loading, user, isAnonymous, messages, isEmailUpgradeModalOpen]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !character || !user) return;



    // Frontend enforcement: Prevent sending more than 3 messages for anonymous users
    if (isAnonymous && anonymousMessageCount >= 3) {
      console.log('🚫 Anonymous user trying to send 4th+ message, showing email upgrade modal');
      setMessageCount(anonymousMessageCount);
      setIsEmailUpgradeModalOpen(true);
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setUsageLimitError(null); // Clear any previous usage errors

    try {
      // Call the chat edge function
      // Pass null for conversation_id if this is a new conversation (currentConversationId is null)
      // Pass the actual conversation_id if continuing an existing conversation
      const { data, error } = await supabase.functions.invoke('api/chat', {
        method: 'POST',
        body: {
          character: character.id,
          conversation_id: currentConversationId, // null for new, UUID for existing
          user_chat: userMessage.content
        },
      });

      if (error) {
        // Check if this is a usage limit error (HTTP 429) - now only for subscription limits
        if (error.status === 429 || error.message?.includes('limit_exceeded')) {
          const usageError = error.context as UsageLimitError;
          setUsageLimitError(usageError);
          
          // Remove the user message since it wasn't processed
          setMessages(prev => prev.slice(0, -1));
          setInputValue(userMessage.content); // Restore the input
          
          // Show PAYG modal for subscription limit exceeded (backend limit)
          setIsPaygModalOpen(true);
          return;
        }
        throw error;
      }

      // If this was a new conversation, update the conversation ID from the response
      if (!currentConversationId && data.conversation_id) {
        setCurrentConversationId(data.conversation_id);
      }

      // Message successfully sent - trigger handles count automatically
      console.log('💬 Chat: Message sent successfully, trigger will update usage count');

      const characterResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: data.openai_response || data.response || "Sorry, I seem to have lost my words. Try again, will you?",
        sender: 'character',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, characterResponse]);
    } catch (error) {
      console.error('Chat function error:', error);
      
      // Remove the user message since it wasn't processed successfully
      setMessages(prev => prev.slice(0, -1));
      setInputValue(userMessage.content); // Restore the input
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: character.errorMessage,
        sender: 'character',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleUpgrade = () => {
    navigate('/pricing');
  };

  const handlePaygPurchaseComplete = () => {
    // Clear usage limit error and allow user to continue chatting
    setUsageLimitError(null);
    setIsPaygModalOpen(false);
  };

  const handleEmailUpgradeComplete = () => {
    // Clear usage limit error and close modal
    setUsageLimitError(null);
    setIsEmailUpgradeModalOpen(false);
    setAnonymousMessageCount(0);
    setMessageCount(0);
    
    console.log('✅ Email upgrade completed successfully');
    
    // The AuthContext will automatically update isAnonymous and needsEmailUpgrade state
    // After successful email upgrade, user can continue messaging with their subscription limits
  };

  // Enhanced message count tracking for anonymous users
  useEffect(() => {
    if (isAnonymous && messages.length > 0) {
      // Count only user messages for anonymous limit tracking
      const userMessageCount = messages.filter(m => m.sender === 'user').length;
      setAnonymousMessageCount(userMessageCount);
      console.log('👤 Anonymous user message count:', userMessageCount);
    } else if (isAnonymous && messages.length === 0) {
      // Reset count when no messages
      setAnonymousMessageCount(0);
      console.log('👤 Anonymous user message count reset to 0');
    }
  }, [messages, isAnonymous]);

  // Show email upgrade modal when anonymous user reaches 3 messages
  useEffect(() => {
    if (isAnonymous && anonymousMessageCount === 3 && !isEmailUpgradeModalOpen) {
      console.log('🎯 Anonymous user reached 3 messages, showing email upgrade modal');
      setMessageCount(3);
      setIsEmailUpgradeModalOpen(true);
    }
  }, [isAnonymous, anonymousMessageCount, isEmailUpgradeModalOpen]);

  // Enhanced input blocking logic
  const isInputBlocked = !!usageLimitError || 
    (isAnonymous && anonymousMessageCount >= 3);

  // Show loading while auth is being checked or character is being loaded
  if (loading || !user || !character) {
    return (
      <div className="min-h-screen bg-noir-gradient flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 animate-spin text-noir-amber mx-auto mb-4 border-2 border-noir-amber border-t-transparent rounded-full"></div>
          <p className="text-noir-cream/70">
            {loading ? 'Authenticating...' : !user ? 'Redirecting...' : 'Loading character...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-noir-gradient">
      <Header />
      
      {/* Chat Header */}
      <div className="sticky top-0 z-10 bg-noir-charcoal/95 backdrop-blur-sm border-b border-noir-amber/20 p-4">
        <div className="max-w-4xl mx-auto flex items-center space-x-4">
          <Button
            onClick={() => navigate('/')}
            variant="ghost"
            className="text-noir-amber hover:text-noir-warm-amber font-source-sans"
          >
            ← Back to Table
          </Button>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-noir-burgundy text-noir-cream flex items-center justify-center text-lg font-bold border border-noir-amber">
              {character.avatar}
            </div>
            <div>
              <h2 className="font-hollywood text-lg font-semibold text-noir-amber">{character.name}</h2>
              <p className="text-sm text-noir-cream/60 font-source-sans">{character.title}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Restaurant Booth Background */}
      <div className="flex-1 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-noir-deep-charcoal via-noir-charcoal to-noir-shadow"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="booth-leather h-full"></div>
        </div>

        {/* Usage Limit Error Alert */}
        {usageLimitError && (
          <div className="relative max-w-4xl mx-auto p-6">
            <Alert className="bg-noir-burgundy/20 border-noir-amber/50 text-noir-cream">
              <AlertCircle className="h-4 w-4 text-noir-amber" />
              <AlertDescription className="font-source-sans">
                <div className="space-y-3">
                  <p className="font-semibold">{usageLimitError.message}</p>
                  <div className="text-sm text-noir-cream/80">
                    <p>Current usage: {usageLimitError.currentUsage.messages}/{usageLimitError.limits.messages} messages</p>
                    <p>Subscription: {usageLimitError.subscriptionTier}</p>
                  </div>
                  <div className="flex gap-3">
                    <Button 
                      onClick={() => setIsPaygModalOpen(true)}
                      className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-source-sans"
                    >
                      <CreditCard className="w-4 h-4 mr-2" />
                      Buy Credits
                    </Button>
                    <Button 
                      onClick={handleUpgrade}
                      variant="outline"
                      className="border-noir-amber/50 text-noir-amber hover:bg-noir-amber/10 font-source-sans"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      Upgrade Plan
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Chat Messages */}
        <div className="relative max-w-4xl mx-auto p-6 space-y-6">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-2xl ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
                <Card className={`p-4 ${
                  message.sender === 'user'
                    ? 'bg-noir-shadow border-noir-cream/20 text-noir-cream'
                    : 'bg-noir-burgundy/20 border-noir-amber/30 text-noir-cream'
                }`}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      message.sender === 'user'
                        ? 'bg-noir-cream text-noir-charcoal'
                        : 'bg-noir-amber text-noir-charcoal'
                    }`}>
                      {message.sender === 'user' ? <User className="w-4 h-4" /> : character.avatar}
                    </div>
                    <div className="flex-1">
                      <p className="leading-relaxed font-sans whitespace-pre-line">{message.content}</p>
                      <p className="text-xs text-noir-cream/50 mt-2 font-source-sans">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="flex justify-start">
              <Card className="bg-noir-burgundy/20 border-noir-amber/30 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-noir-amber text-noir-charcoal flex items-center justify-center text-sm font-bold">
                    {character.avatar}
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-noir-amber rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-noir-amber rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-noir-amber rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="sticky bottom-0 bg-noir-charcoal/95 backdrop-blur-sm border-t border-noir-amber/20 p-4">
          <div className="max-w-4xl mx-auto">
            
            
            <div className="flex items-end space-x-3">
              <div className="flex-1">
                <Textarea
                  ref={textareaRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={character.placeholderText}
                  className="min-h-[60px] bg-noir-input-bg border-noir-input-border text-noir-input-text placeholder:text-noir-input-placeholder resize-none focus:border-noir-input-focus font-source-sans transition-all duration-300"
                  disabled={isInputBlocked} // Disable input when usage limit is exceeded
                />
              </div>
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isTyping || isInputBlocked}
                className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber h-[60px] px-6 font-source-sans"
              >
                <ArrowUp className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* PAYG Credits Modal */}
      <PaygCreditsModal 
        isOpen={isPaygModalOpen}
        onClose={() => setIsPaygModalOpen(false)}
        onPurchaseComplete={handlePaygPurchaseComplete}
      />

      {/* Email Upgrade Modal for Anonymous Users */}
      <EmailUpgradeModal 
        isOpen={isEmailUpgradeModalOpen}
        onClose={() => {
          // Only allow closing if it's not required (user hasn't hit limits)
          if (!needsEmailUpgrade && anonymousMessageCount < 3) {
            setIsEmailUpgradeModalOpen(false);
          }
        }}
        messageCount={messageCount}
        isRequired={anonymousMessageCount >= 3}
        onUpgradeComplete={handleEmailUpgradeComplete}
      />
    </div>
  );
};

export default Chat;
